import { type NextRequest, NextResponse } from "next/server";
import { updateSession } from "./utils/supabase/middleware";

// Simple in-memory rate limiting (for basic protection)
// Note: This will reset on server restart, but it's sufficient for basic protection
const requestCounts = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT_WINDOW_MS = 10 * 1000; // 10 seconds
const RATE_LIMIT_MAX_REQUESTS = 10; // 10 requests per window

function isRateLimited(ip: string): { limited: boolean; remaining: number; reset: number } {
  const now = Date.now();
  const key = ip;

  let record = requestCounts.get(key);

  // Reset if window has expired
  if (!record || now > record.resetTime) {
    record = {
      count: 1,
      resetTime: now + RATE_LIMIT_WINDOW_MS
    };
    requestCounts.set(key, record);
    return {
      limited: false,
      remaining: RATE_LIMIT_MAX_REQUESTS - 1,
      reset: record.resetTime
    };
  }

  // Increment count
  record.count++;

  const remaining = Math.max(0, RATE_LIMIT_MAX_REQUESTS - record.count);
  const limited = record.count > RATE_LIMIT_MAX_REQUESTS;

  return {
    limited,
    remaining,
    reset: record.resetTime
  };
}


export async function middleware(request: NextRequest) {
  // --- Domain and HTTPS Redirect Logic START ---
  const url = request.nextUrl.clone();
  const hostname = url.hostname;
  const protocol = url.protocol;

  // Only apply redirects in production environment and exclude development/testing domains
  const isDevelopmentDomain = hostname.includes('localhost') ||
                              hostname.includes('ngrok.io') ||
                              hostname.includes('ngrok-free.app') ||
                              hostname.includes('127.0.0.1');

  if (process.env.NODE_ENV === 'production' && !isDevelopmentDomain) {
    let shouldRedirect = false;

    // Check for www redirect (www.dukancard.in -> dukancard.in)
    if (hostname.startsWith('www.')) {
      url.hostname = hostname.replace('www.', '');
      shouldRedirect = true;
    }

    // Check for HTTPS redirect (http:// -> https://)
    if (protocol === 'http:') {
      url.protocol = 'https:';
      shouldRedirect = true;
    }

    // Perform redirect if needed
    if (shouldRedirect) {
      return NextResponse.redirect(url.toString(), 301); // Permanent redirect
    }
  }
  // --- Domain and HTTPS Redirect Logic END ---

  // --- Rate Limiting Logic START ---
  // Apply rate limiting to API routes only (skip webhooks)
  if (request.nextUrl.pathname.startsWith("/api/") && !request.nextUrl.pathname.startsWith("/api/webhooks/")) {
    // Get IP address: Check 'x-forwarded-for' header first, then fallback.
    const forwardedFor = request.headers.get('x-forwarded-for');
    // The header can contain multiple IPs (client, proxy1, proxy2). The client IP is usually the first one.
    const ip = forwardedFor ? forwardedFor.split(',')[0].trim() : "127.0.0.1";

    try {
      // Use simple in-memory rate limiting
      const { limited, remaining, reset } = isRateLimited(ip);

      if (limited) {
        // Rate limit exceeded, return 429
        return new NextResponse("Too Many Requests", {
          status: 429,
          headers: {
            "X-RateLimit-Limit": RATE_LIMIT_MAX_REQUESTS.toString(),
            "X-RateLimit-Remaining": remaining.toString(),
            "X-RateLimit-Reset": new Date(reset).toISOString(),
          },
        });
      }
    } catch (error) {
      console.error("Rate limiting error:", error);
      // If rate limiting fails, allow the request to proceed
    }
  }
  // --- Rate Limiting Logic END ---


  // Proceed with Supabase session update
  return await updateSession(request);
}

export const config = {
  matcher: [
    /*
     * Match all request paths except forhe ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * Feel free to modify this pattern to include more paths.
     */
    "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
  ],
};